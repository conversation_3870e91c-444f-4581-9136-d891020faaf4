package com.fishing.dto.moment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 动态列表请求的数据传输对象
 */
@Data
@Schema(description = "动态列表请求参数")
public class MomentListDTO {

    /**
     * 页码
     */
    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    /**
     * 用户ID（可选，用于筛选特定用户的动态）
     */
    @Schema(description = "用户ID（可选，用于筛选特定用户的动态）")
    private Long userId;

    /**
     * 标签筛选
     */
    @Schema(description = "标签筛选")
    private String tag;

    /**
     * 省份筛选
     */
    @Schema(description = "省份筛选")
    private String province;

    /**
     * 城市筛选
     */
    @Schema(description = "城市筛选")
    private String city;

    /**
     * 县区筛选
     */
    @Schema(description = "县区筛选")
    private String county;

    /**
     * 动态类型筛选
     */
    @Schema(description = "动态类型筛选（fishing_catch/equipment/technique/question）")
    private String momentType;
}
