import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/utils/date_time_util.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late CommunityViewModel _communityViewModel;
  late AnimationController _fabAnimationController;

  String _selectedFilter = "全部";
  final List<String> _filterOptions = ["全部", "钓获分享", "装备展示", "技巧分享", "问答求助"];

  String _sortBy = "latest"; // latest, hottest
  List<String> _selectedTags = [];
  bool _showOnlyFollowing = false;

  // 控制FAB显示/隐藏
  bool _showFab = true;
  double _lastScrollPosition = 0;

  @override
  void initState() {
    super.initState();
    _communityViewModel = context.read<CommunityViewModel>();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _fabAnimationController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialLoad();
    });

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initialLoad() async {
    await _loadData(refresh: true);
  }

  void _scrollListener() {
    // 加载更多
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_communityViewModel.isLoading && _communityViewModel.hasMore) {
        _loadMoreData();
      }
    }

    // FAB 显示/隐藏动画
    final currentPosition = _scrollController.position.pixels;
    if (currentPosition > _lastScrollPosition &&
        _showFab &&
        currentPosition > 100) {
      setState(() => _showFab = false);
      _fabAnimationController.reverse();
    } else if (currentPosition < _lastScrollPosition && !_showFab) {
      setState(() => _showFab = true);
      _fabAnimationController.forward();
    }
    _lastScrollPosition = currentPosition;
  }

  Future<void> _loadData({bool refresh = false}) async {
    if (_communityViewModel.isLoading && !refresh) return;

    try {
      _applyFiltersToViewModel();
      await _communityViewModel.loadMoments(refresh: refresh);
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) setState(() {});
    }
  }

  Future<void> _loadMoreData() async {
    if (_communityViewModel.isLoading || !_communityViewModel.hasMore) return;

    try {
      await _communityViewModel.loadMoments();
      if (mounted) setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载更多失败: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _applyFiltersToViewModel() {
    // 设置动态类型筛选
    _communityViewModel.setMomentTypeFilter(_selectedFilter);

    // 设置排序方式
    _communityViewModel.setSortBy(_sortBy);

    // 设置是否只显示关注的人
    _communityViewModel.setShowOnlyFollowing(_showOnlyFollowing);

    // 同步标签筛选状态
    // 清除ViewModel中的标签，然后添加当前选中的标签
    final currentViewModelTags =
        List<String>.from(_communityViewModel.selectedTags);
    for (String tag in currentViewModelTags) {
      if (!_selectedTags.contains(tag)) {
        _communityViewModel.toggleTag(tag);
      }
    }

    for (String tag in _selectedTags) {
      if (!_communityViewModel.selectedTags.contains(tag)) {
        _communityViewModel.toggleTag(tag);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final communityViewModel = context.watch<CommunityViewModel>();
    final isLoading = communityViewModel.isLoading;
    final moments = communityViewModel.moments;
    final hasMore = communityViewModel.hasMore;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: RefreshIndicator(
        onRefresh: () => _loadData(refresh: true),
        child: CustomScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(),
          slivers: [
            // 简化的 App Bar
            SliverAppBar(
              floating: true,
              backgroundColor: Colors.white,
              elevation: 0,
              automaticallyImplyLeading: false,
              title: const Text(
                '社区',
                style: TextStyle(
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
              actions: [
                // 排序按钮
                IconButton(
                  onPressed: _showSortOptions,
                  icon: Icon(
                    _sortBy == 'latest'
                        ? Icons.schedule
                        : Icons.local_fire_department,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                // 搜索按钮
                IconButton(
                  onPressed: _showSearchDialog,
                  icon: Icon(Icons.search, color: Colors.grey.shade700),
                ),
                // 通知按钮
                IconButton(
                  onPressed: _showNotifications,
                  icon: Stack(
                    children: [
                      Icon(Icons.notifications_outlined,
                          color: Colors.grey.shade700),
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),

            // 优化的筛选栏
            SliverPersistentHeader(
              pinned: true,
              delegate: _OptimizedFilterBarDelegate(
                child: Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      // 主筛选栏
                      Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: [
                            // 快速筛选：仅关注
                            if (isAuthenticated())
                              GestureDetector(
                                onTap: () {
                                  HapticFeedback.selectionClick();
                                  setState(() =>
                                      _showOnlyFollowing = !_showOnlyFollowing);
                                  _communityViewModel
                                      .setShowOnlyFollowing(_showOnlyFollowing);
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: _showOnlyFollowing
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade100,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _showOnlyFollowing
                                            ? Icons.people
                                            : Icons.people_outline,
                                        size: 16,
                                        color: _showOnlyFollowing
                                            ? Colors.white
                                            : Colors.grey.shade700,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '关注',
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: _showOnlyFollowing
                                              ? Colors.white
                                              : Colors.grey.shade700,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                            if (isAuthenticated()) const SizedBox(width: 8),

                            // 类型筛选
                            Expanded(
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: _filterOptions.length,
                                itemBuilder: (context, index) {
                                  final filter = _filterOptions[index];
                                  final isSelected = _selectedFilter == filter;
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: GestureDetector(
                                      onTap: () {
                                        HapticFeedback.selectionClick();
                                        setState(
                                            () => _selectedFilter = filter);
                                        _loadData(refresh: true);
                                      },
                                      child: AnimatedContainer(
                                        duration:
                                            const Duration(milliseconds: 200),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16, vertical: 8),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? Theme.of(context).primaryColor
                                              : Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          border: Border.all(
                                            color: isSelected
                                                ? Theme.of(context).primaryColor
                                                : Colors.grey.shade300,
                                            width: 1,
                                          ),
                                        ),
                                        child: Text(
                                          filter,
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : Colors.grey.shade700,
                                            fontSize: 13,
                                            fontWeight: isSelected
                                                ? FontWeight.w600
                                                : FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),

                            // 更多筛选
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: _showAdvancedFilters,
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: _selectedTags.isNotEmpty
                                      ? Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.1)
                                      : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Badge(
                                  isLabelVisible: _selectedTags.isNotEmpty,
                                  label: Text(_selectedTags.length.toString()),
                                  child: Icon(
                                    Icons.filter_list,
                                    size: 20,
                                    color: _selectedTags.isNotEmpty
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade700,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 标签展示栏（如果有选中的标签）
                      if (_selectedTags.isNotEmpty)
                        Container(
                          height: 36,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            children: _selectedTags
                                .map((tag) => Container(
                                      margin: const EdgeInsets.only(right: 8),
                                      child: Chip(
                                        label: Text(tag),
                                        deleteIcon:
                                            const Icon(Icons.close, size: 16),
                                        onDeleted: () {
                                          setState(
                                              () => _selectedTags.remove(tag));
                                          _loadData(refresh: true);
                                        },
                                        backgroundColor: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.1),
                                        deleteIconColor:
                                            Theme.of(context).primaryColor,
                                        labelStyle: TextStyle(
                                          color: Theme.of(context).primaryColor,
                                          fontSize: 12,
                                        ),
                                        padding: EdgeInsets.zero,
                                        visualDensity: VisualDensity.compact,
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // 内容区域
            if (isLoading && moments.isEmpty)
              SliverToBoxAdapter(
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.5,
                  alignment: Alignment.center,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              _buildOptimizedMomentsList(moments, isLoading, hasMore),
          ],
        ),
      ),

      // 优化的 FAB
      floatingActionButton: ScaleTransition(
        scale: _fabAnimationController,
        child: FloatingActionButton.extended(
          onPressed: _showPublishOptions,
          backgroundColor: Theme.of(context).primaryColor,
          icon: const Icon(Icons.edit),
          label: const Text('发布'),
        ),
      ),
    );
  }

  // 优化的动态列表
  Widget _buildOptimizedMomentsList(moments, bool isLoading, bool hasMore) {
    if (moments.isEmpty && !isLoading) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.bubble_chart_outlined,
                size: 80,
                color: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                _showOnlyFollowing ? '还没有关注的人发布动态' : '暂无动态',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _showOnlyFollowing ? '去发现更多有趣的钓友吧' : '成为第一个分享的人',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),
              OutlinedButton.icon(
                onPressed: _showOnlyFollowing
                    ? () {
                        setState(() => _showOnlyFollowing = false);
                        _communityViewModel.setShowOnlyFollowing(false);
                      }
                    : _showPublishOptions,
                icon: Icon(_showOnlyFollowing ? Icons.explore : Icons.add),
                label: Text(_showOnlyFollowing ? '发现动态' : '发布动态'),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == moments.length) {
            if (isLoading) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              );
            } else if (!hasMore) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: Column(
                    children: [
                      Icon(Icons.check_circle_outline,
                          color: Colors.grey.shade400, size: 32),
                      const SizedBox(height: 8),
                      Text(
                        '已经到底了',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
            return const SizedBox(height: 80);
          }

          return _buildOptimizedMomentCard(moments[index]);
        },
        childCount: moments.isEmpty ? 0 : moments.length + 1,
      ),
    );
  }

  // 优化的动态卡片
  Widget _buildOptimizedMomentCard(moment) {
    final bool isLiked = moment.liked ?? false;
    final bool hasImages = moment.images != null && moment.images!.isNotEmpty;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showMomentDetail(moment),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息头部 - 简化设计
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 头像
                  GestureDetector(
                    onTap: () => _showUserProfile(moment.publisher),
                    child: Hero(
                      tag: 'avatar_${moment.id}',
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          image: moment.publisher.avatarUrl != null
                              ? DecorationImage(
                                  image:
                                      NetworkImage(moment.publisher.avatarUrl!),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: Colors.grey.shade200,
                        ),
                        child: moment.publisher.avatarUrl == null
                            ? Icon(Icons.person,
                                color: Colors.grey.shade400, size: 20)
                            : null,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 用户信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              moment.publisher.name.isNotEmpty
                                  ? moment.publisher.name
                                  : '匿名用户',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                              ),
                            ),
                            if (moment.momentType != null) ...[
                              const SizedBox(width: 8),
                              _buildMomentTypeTag(moment.momentType),
                            ],
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text(
                              _formatTimeAgo(moment.createdAt),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade500,
                              ),
                            ),
                            if (moment.fishingSpotName != null) ...[
                              const SizedBox(width: 8),
                              Text('·',
                                  style:
                                      TextStyle(color: Colors.grey.shade500)),
                              const SizedBox(width: 8),
                              Flexible(
                                child: GestureDetector(
                                  onTap: () => _navigateToFishingSpot(
                                      moment.fishingSpotId),
                                  child: Text(
                                    moment.fishingSpotName!,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 更多操作
                  IconButton(
                    onPressed: () => _showMoreOptions(moment),
                    icon: Icon(Icons.more_horiz, color: Colors.grey.shade600),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // 内容区域
            if (moment.content != null && moment.content!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                child: Text(
                  moment.content!,
                  style: const TextStyle(
                    fontSize: 15,
                    height: 1.5,
                  ),
                  maxLines: 5,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

            // 图片展示 - 优化布局
            if (hasImages) _buildOptimizedImageSection(moment.images!),

            // 标签
            if (moment.tags != null && moment.tags!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                child: Wrap(
                  spacing: 8,
                  children: moment.tags!
                      .map<Widget>((tag) => GestureDetector(
                            onTap: () => _searchByTag(tag),
                            child: Text(
                              '#$tag',
                              style: TextStyle(
                                fontSize: 13,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ),

            // 交互栏 - 更简洁的设计
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 点赞
                  _buildInteractionItem(
                    icon: isLiked ? Icons.favorite : Icons.favorite_border,
                    count: moment.likeCount ?? 0,
                    color: isLiked ? Colors.red : null,
                    onTap: () => _toggleLike(moment.id),
                  ),
                  const SizedBox(width: 24),

                  // 评论
                  _buildInteractionItem(
                    icon: Icons.mode_comment_outlined,
                    count: moment.commentCount ?? 0,
                    onTap: () => _showMomentDetail(moment),
                  ),

                  const Spacer(),

                  // 分享和收藏
                  IconButton(
                    onPressed: () => _shareMoment(moment),
                    icon: Icon(Icons.share_outlined, size: 20),
                    color: Colors.grey.shade600,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 16),
                  IconButton(
                    onPressed: () => _toggleBookmark(moment.id),
                    icon: Icon(
                      moment.bookmarked ?? false
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      size: 20,
                    ),
                    color: moment.bookmarked ?? false
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade600,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 优化的图片展示
  Widget _buildOptimizedImageSection(List images) {
    if (images.length == 1) {
      // 单图 - 大图展示
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: Image.network(
              images[0].imageUrl ?? images[0],
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey.shade200,
                  child: Icon(Icons.broken_image, color: Colors.grey.shade400),
                );
              },
            ),
          ),
        ),
      );
    }

    // 多图 - 网格展示
    final displayCount = images.length > 4 ? 4 : images.length;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: displayCount,
        itemBuilder: (context, index) {
          final isLast = index == displayCount - 1;
          final remainingCount = images.length - displayCount;

          return Stack(
            fit: StackFit.expand,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  images[index].imageUrl ?? images[index],
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey.shade200,
                      child:
                          Icon(Icons.broken_image, color: Colors.grey.shade400),
                    );
                  },
                ),
              ),
              if (isLast && remainingCount > 0)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '+$remainingCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  // 交互项组件
  Widget _buildInteractionItem({
    required IconData icon,
    required int count,
    Color? color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Row(
        children: [
          Icon(
            icon,
            size: 22,
            color: color ?? Colors.grey.shade700,
          ),
          if (count > 0) ...[
            const SizedBox(width: 4),
            Text(
              count > 999 ? '999+' : count.toString(),
              style: TextStyle(
                fontSize: 14,
                color: color ?? Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 动态类型标签
  Widget _buildMomentTypeTag(String? momentType) {
    if (momentType == null) return const SizedBox.shrink();

    String displayText;
    IconData icon;
    Color color;

    switch (momentType) {
      case 'fishing_catch':
        displayText = '钓获';
        icon = Icons.catching_pokemon;
        color = Colors.green;
        break;
      case 'equipment':
        displayText = '装备';
        icon = Icons.construction;
        color = Colors.blue;
        break;
      case 'technique':
        displayText = '技巧';
        icon = Icons.lightbulb_outline;
        color = Colors.orange;
        break;
      case 'question':
        displayText = '求助';
        icon = Icons.help_outline;
        color = Colors.purple;
        break;
      default:
        displayText = '动态';
        icon = Icons.article_outlined;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 3),
          Text(
            displayText,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // 排序选项
  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    '排序方式',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('最新发布'),
                  trailing: _sortBy == 'latest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    setState(() => _sortBy = 'latest');
                    _communityViewModel.setSortBy('latest');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.local_fire_department),
                  title: const Text('最热门'),
                  subtitle: const Text('按互动量排序'),
                  trailing: _sortBy == 'hottest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    setState(() => _sortBy = 'hottest');
                    _communityViewModel.setSortBy('hottest');
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  // 高级筛选
  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        List<String> localSelectedTags = List.from(_selectedTags);

        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '标签筛选',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.w600),
                        ),
                        TextButton(
                          onPressed: () =>
                              setState(() => localSelectedTags.clear()),
                          child: const Text('清除'),
                        ),
                      ],
                    ),
                  ),

                  // Tags
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          '新手入门',
                          '路亚技巧',
                          '台钓',
                          '野钓',
                          '黑坑',
                          '饵料配方',
                          '装备推荐',
                          '钓点分享',
                          '冬季钓鱼',
                          '鲫鱼',
                          '鲤鱼',
                          '草鱼',
                          '鲢鳙',
                          '翘嘴',
                          '鲈鱼'
                        ].map((tag) {
                          final isSelected = localSelectedTags.contains(tag);
                          return FilterChip(
                            label: Text(tag),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  localSelectedTags.add(tag);
                                } else {
                                  localSelectedTags.remove(tag);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ),
                  ),

                  // Apply button
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          this.setState(
                              () => _selectedTags = localSelectedTags);
                          _loadData(refresh: true);
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child:
                            const Text('应用筛选', style: TextStyle(fontSize: 16)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // 发布选项 - 优化设计
  void _showPublishOptions() {
    if (!isAuthenticated()) {
      _showLoginDialog('发布动态');
      return;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    '发布动态',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ),

                // 发布选项网格
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                            icon: Icons.set_meal,
                            label: '钓获分享',
                            color: Colors.green,
                            onTap: () {
                              Navigator.pop(context);
                              context.push(AppRoutes.publishMoment,
                                  extra: '钓获分享');
                            },
                          ),
                          _buildPublishOption(
                            icon: Icons.backpack,
                            label: '装备展示',
                            color: Colors.blue,
                            onTap: () {
                              Navigator.pop(context);
                              context.push(AppRoutes.publishMoment,
                                  extra: '装备展示');
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                            icon: Icons.tips_and_updates,
                            label: '技巧分享',
                            color: Colors.orange,
                            onTap: () {
                              Navigator.pop(context);
                              context.push(AppRoutes.publishMoment,
                                  extra: '技巧分享');
                            },
                          ),
                          _buildPublishOption(
                            icon: Icons.help_outline,
                            label: '问答求助',
                            color: Colors.purple,
                            onTap: () {
                              Navigator.pop(context);
                              context.push(AppRoutes.publishMoment,
                                  extra: '问答求助');
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPublishOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: color, size: 32),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // 其他辅助方法
  void _showMoreOptions(moment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.bookmark_outline),
                  title: const Text('收藏动态'),
                  onTap: () {
                    Navigator.pop(context);
                    _toggleBookmark(moment.id);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.share_outlined),
                  title: const Text('分享动态'),
                  onTap: () {
                    Navigator.pop(context);
                    _shareMoment(moment);
                  },
                ),
                if (!_isOwnMoment(moment))
                  ListTile(
                    leading: const Icon(Icons.person_add_outlined),
                    title: Text(moment.followed ?? false ? '取消关注' : '关注作者'),
                    onTap: () {
                      Navigator.pop(context);
                      _toggleFollow(moment.publisher.id);
                    },
                  ),
                ListTile(
                  leading: const Icon(Icons.flag_outlined),
                  title: const Text('举报'),
                  onTap: () {
                    Navigator.pop(context);
                    _reportMoment(moment);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Actions
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String searchQuery = '';
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('搜索动态'),
          content: TextField(
            autofocus: true,
            decoration: const InputDecoration(
              hintText: '输入关键词搜索...',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => searchQuery = value,
            onSubmitted: (value) {
              Navigator.pop(context);
              if (value.trim().isNotEmpty) {
                _communityViewModel.searchWithFilters(value.trim());
              }
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                if (searchQuery.trim().isNotEmpty) {
                  _communityViewModel.searchWithFilters(searchQuery.trim());
                }
              },
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('搜索'),
            ),
          ],
        );
      },
    );
  }

  void _showNotifications() {
    if (!isAuthenticated()) {
      _showLoginDialog('查看通知');
      return;
    }

    // 导航到通知页面
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '通知',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                    TextButton(
                      onPressed: () {
                        // 标记所有为已读
                      },
                      child: const Text('全部已读'),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.notifications_none,
                        size: 80,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '暂无新通知',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showMomentDetail(moment) {
    context.pushNamed(
      'moment_detail',
      pathParameters: {'id': moment.id.toString()},
      extra: moment,
    );
  }

  void _showUserProfile(user) {
    // 导航到用户主页
    context.pushNamed(
      'user_profile',
      pathParameters: {'userId': user.id.toString()},
      extra: user,
    );
  }

  void _toggleLike(int momentId) {
    _communityViewModel.toggleLikeMoment(momentId);
  }

  void _toggleFollow(int userId) {
    _communityViewModel.toggleFollowUser(userId);
  }

  void _toggleBookmark(int momentId) {
    if (!isAuthenticated()) {
      _showLoginDialog('收藏动态');
      return;
    }
    _communityViewModel.toggleBookmark(momentId);
  }

  void _shareMoment(moment) {
    // 实现分享功能
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    '分享动态',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.copy),
                  title: const Text('复制链接'),
                  onTap: () {
                    Navigator.pop(context);
                    // 复制链接到剪贴板
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('链接已复制到剪贴板'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.share),
                  title: const Text('分享到其他应用'),
                  onTap: () {
                    Navigator.pop(context);
                    // 调用系统分享
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  void _reportMoment(moment) {
    if (!isAuthenticated()) {
      _showLoginDialog('举报动态');
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return FutureBuilder<List<String>>(
          future: _communityViewModel.getReportReasons(),
          builder: (context, snapshot) {
            final reasons = snapshot.data ??
                ['垃圾信息', '违法违规', '色情内容', '暴力内容', '虚假信息', '侵犯版权', '其他'];

            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      '举报动态',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: reasons.length,
                      itemBuilder: (context, index) {
                        final reason = reasons[index];
                        return ListTile(
                          title: Text(reason),
                          onTap: () async {
                            Navigator.pop(context);
                            try {
                              await _communityViewModel.reportMoment(
                                  moment.id, reason);
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('举报已提交，我们会尽快处理'),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('举报失败: $e'),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            }
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _navigateToFishingSpot(int? fishingSpotId) {
    if (fishingSpotId != null) {
      context.pushNamed(
        'fishing_spot_detail',
        pathParameters: {'id': fishingSpotId.toString()},
      );
    }
  }

  void _searchByTag(String tag) {
    setState(() {
      if (!_selectedTags.contains(tag)) {
        _selectedTags.add(tag);
      }
    });
    _loadData(refresh: true);
  }

  bool _isOwnMoment(moment) {
    final authViewModel = context.read<AuthViewModel>();
    return authViewModel.currentUser?.id == moment.publisher.id;
  }

  String _formatTimeAgo(String? createdAt) {
    if (createdAt == null) return '';

    try {
      final dateTime = DateTime.parse(createdAt);
      return DateTimeUtil.formatTime(dateTime);
    } catch (e) {
      return createdAt;
    }
  }

  void _showLoginDialog(String action) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('需要登录'),
          content: Text('您需要先登录才能$action'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                context.push(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }

  bool isAuthenticated() {
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);
    return authViewModel.isUserLoggedIn();
  }
}

// 优化的筛选栏代理
class _OptimizedFilterBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _OptimizedFilterBarDelegate({required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      elevation: overlapsContent ? 4 : 0,
      child: child,
    );
  }

  @override
  double get maxExtent => 84;

  @override
  double get minExtent => 48;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
