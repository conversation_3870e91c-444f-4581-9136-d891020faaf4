// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'moment_list_request.dart';

class MomentListRequestMapper extends ClassMapperBase<MomentListRequest> {
  MomentListRequestMapper._();

  static MomentListRequestMapper? _instance;
  static MomentListRequestMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = MomentListRequestMapper._());
      RequestPageInfoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'MomentListRequest';

  static num? _$pageNum(MomentListRequest v) => v.pageNum;
  static const Field<MomentListRequest, num> _f$pageNum =
      Field('pageNum', _$pageNum, key: r'page_num', opt: true);
  static num? _$pageSize(MomentListRequest v) => v.pageSize;
  static const Field<MomentListRequest, num> _f$pageSize =
      Field('pageSize', _$pageSize, key: r'page_size', opt: true);
  static num? _$userId(MomentListRequest v) => v.userId;
  static const Field<MomentListRequest, num> _f$userId =
      Field('userId', _$userId, key: r'user_id', opt: true);
  static String? _$tag(MomentListRequest v) => v.tag;
  static const Field<MomentListRequest, String> _f$tag =
      Field('tag', _$tag, opt: true);
  static String? _$province(MomentListRequest v) => v.province;
  static const Field<MomentListRequest, String> _f$province =
      Field('province', _$province, opt: true);
  static String? _$city(MomentListRequest v) => v.city;
  static const Field<MomentListRequest, String> _f$city =
      Field('city', _$city, opt: true);
  static String? _$county(MomentListRequest v) => v.county;
  static const Field<MomentListRequest, String> _f$county =
      Field('county', _$county, opt: true);
  static String? _$momentType(MomentListRequest v) => v.momentType;
  static const Field<MomentListRequest, String> _f$momentType =
      Field('momentType', _$momentType, key: r'moment_type', opt: true);

  @override
  final MappableFields<MomentListRequest> fields = const {
    #pageNum: _f$pageNum,
    #pageSize: _f$pageSize,
    #userId: _f$userId,
    #tag: _f$tag,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #momentType: _f$momentType,
  };

  static MomentListRequest _instantiate(DecodingData data) {
    return MomentListRequest(
        pageNum: data.dec(_f$pageNum),
        pageSize: data.dec(_f$pageSize),
        userId: data.dec(_f$userId),
        tag: data.dec(_f$tag),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        momentType: data.dec(_f$momentType));
  }

  @override
  final Function instantiate = _instantiate;

  static MomentListRequest fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<MomentListRequest>(map);
  }

  static MomentListRequest fromJson(String json) {
    return ensureInitialized().decodeJson<MomentListRequest>(json);
  }
}

mixin MomentListRequestMappable {
  String toJson() {
    return MomentListRequestMapper.ensureInitialized()
        .encodeJson<MomentListRequest>(this as MomentListRequest);
  }

  Map<String, dynamic> toMap() {
    return MomentListRequestMapper.ensureInitialized()
        .encodeMap<MomentListRequest>(this as MomentListRequest);
  }

  MomentListRequestCopyWith<MomentListRequest, MomentListRequest,
          MomentListRequest>
      get copyWith =>
          _MomentListRequestCopyWithImpl<MomentListRequest, MomentListRequest>(
              this as MomentListRequest, $identity, $identity);
  @override
  String toString() {
    return MomentListRequestMapper.ensureInitialized()
        .stringifyValue(this as MomentListRequest);
  }

  @override
  bool operator ==(Object other) {
    return MomentListRequestMapper.ensureInitialized()
        .equalsValue(this as MomentListRequest, other);
  }

  @override
  int get hashCode {
    return MomentListRequestMapper.ensureInitialized()
        .hashValue(this as MomentListRequest);
  }
}

extension MomentListRequestValueCopy<$R, $Out>
    on ObjectCopyWith<$R, MomentListRequest, $Out> {
  MomentListRequestCopyWith<$R, MomentListRequest, $Out>
      get $asMomentListRequest => $base
          .as((v, t, t2) => _MomentListRequestCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class MomentListRequestCopyWith<$R, $In extends MomentListRequest,
    $Out> implements RequestPageInfoCopyWith<$R, $In, $Out> {
  @override
  $R call(
      {num? pageNum,
      num? pageSize,
      num? userId,
      String? tag,
      String? province,
      String? city,
      String? county,
      String? momentType});
  MomentListRequestCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _MomentListRequestCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, MomentListRequest, $Out>
    implements MomentListRequestCopyWith<$R, MomentListRequest, $Out> {
  _MomentListRequestCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<MomentListRequest> $mapper =
      MomentListRequestMapper.ensureInitialized();
  @override
  $R call(
          {Object? pageNum = $none,
          Object? pageSize = $none,
          Object? userId = $none,
          Object? tag = $none,
          Object? province = $none,
          Object? city = $none,
          Object? county = $none,
          Object? momentType = $none}) =>
      $apply(FieldCopyWithData({
        if (pageNum != $none) #pageNum: pageNum,
        if (pageSize != $none) #pageSize: pageSize,
        if (userId != $none) #userId: userId,
        if (tag != $none) #tag: tag,
        if (province != $none) #province: province,
        if (city != $none) #city: city,
        if (county != $none) #county: county,
        if (momentType != $none) #momentType: momentType
      }));
  @override
  MomentListRequest $make(CopyWithData data) => MomentListRequest(
      pageNum: data.get(#pageNum, or: $value.pageNum),
      pageSize: data.get(#pageSize, or: $value.pageSize),
      userId: data.get(#userId, or: $value.userId),
      tag: data.get(#tag, or: $value.tag),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      momentType: data.get(#momentType, or: $value.momentType));

  @override
  MomentListRequestCopyWith<$R2, MomentListRequest, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _MomentListRequestCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
