import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/request_page_info.dart';

part 'moment_list_request.mapper.dart';

@MappableClass()
class MomentListRequest extends RequestPageInfo with MomentListRequestMappable {
  num? userId;
  String? tag;
  String? province;
  String? city;
  String? county;
  String? momentType;

  MomentListRequest({
    super.pageNum = null,
    super.pageSize = null,
    this.userId,
    this.tag,
    this.province,
    this.city,
    this.county,
    this.momentType,
  });
}
